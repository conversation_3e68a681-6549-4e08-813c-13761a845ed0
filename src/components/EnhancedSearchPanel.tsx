"use client"

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Loader2, Search, X, Filter, Settings } from 'lucide-react';
import { MultiSelect } from '@/components/ui/multi-select';
import { DateRangePicker } from '@/components/ui/date-range-picker';

// 搜索配置接口
interface SearchConfigInfo {
  code: string;
  name: string;
  description?: string;
  configType: 'SIMPLE_FILTER' | 'MULTI_FIELD' | 'CROSS_TABLE' | 'CUSTOM_LOGIC';
  filterType: 'input' | 'select' | 'multi_select' | 'date_range' | 'checkbox' | 'range';
  placeholder?: string;
  isAdvanced: boolean;
  displayOrder: number;
  options?: Record<string, unknown>;
  validationRules?: Record<string, unknown>;
}

interface SearchValue {
  configCode: string;
  value: string | string[] | { from?: string; to?: string };
}

interface EnhancedSearchPanelProps {
  database: string;
  onSearch: (searchValues: SearchValue[]) => void;
  onClear: () => void;
  onApplyFilters?: () => void;
  loading?: boolean;
  className?: string;
  pendingFilters?: Record<string, unknown>;
  appliedFilters?: Record<string, unknown>;
  hasFilterChanges?: boolean;
}

export default function EnhancedSearchPanel({
  database,
  onSearch,
  onClear,
  onApplyFilters,
  loading = false,
  className = "",
  pendingFilters = {},
  appliedFilters = {},
  hasFilterChanges = false
}: EnhancedSearchPanelProps) {
  const [searchConfigs, setSearchConfigs] = useState<SearchConfigInfo[]>([]);
  const [searchValues, setSearchValues] = useState<Map<string, any>>(new Map());
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [configsLoading, setConfigsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 加载搜索配置
  useEffect(() => {
    loadSearchConfigs();
  }, [database]);

  const loadSearchConfigs = async () => {
    try {
      setConfigsLoading(true);
      setError(null);
      
      const response = await fetch(`/api/enhanced-search/${database}`);
      if (!response.ok) {
        throw new Error('Failed to load search configurations');
      }
      
      const result = await response.json();
      if (result.success) {
        setSearchConfigs(result.data.searchConfigs || []);
      } else {
        throw new Error(result.error || 'Unknown error');
      }
    } catch (err) {
      console.error('Error loading search configs:', err);
      setError(err instanceof Error ? err.message : 'Failed to load search configurations');
    } finally {
      setConfigsLoading(false);
    }
  };

  // 更新搜索值
  const updateSearchValue = (configCode: string, value: any) => {
    const newValues = new Map(searchValues);
    if (value === '' || value === null || value === undefined || 
        (Array.isArray(value) && value.length === 0)) {
      newValues.delete(configCode);
    } else {
      newValues.set(configCode, value);
    }
    setSearchValues(newValues);
  };

  // 执行搜索 - 统一处理增强搜索和常规筛选器
  const handleSearch = () => {
    // 首先应用常规筛选器（如果有的话）
    if (onApplyFilters && hasFilterChanges) {
      onApplyFilters();
    }

    // 然后执行增强搜索（如果有搜索条件的话）
    if (searchValues.size > 0) {
      const searchData: SearchValue[] = Array.from(searchValues.entries()).map(([configCode, value]) => ({
        configCode,
        value
      }));
      onSearch(searchData);
    }
  };

  // 清除搜索
  const handleClear = () => {
    setSearchValues(new Map());
    onClear();
  };

  // 渲染搜索控件
  const renderSearchControl = (config: SearchConfigInfo) => {
    const currentValue = searchValues.get(config.code);

    switch (config.filterType) {
      case 'input':
        return (
          <Input
            placeholder={config.placeholder || `Enter ${config.name.toLowerCase()}`}
            value={(currentValue as string) || ''}
            onChange={(e) => updateSearchValue(config.code, e.target.value)}
            className="w-full"
            onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
          />
        );

      case 'select':
        const selectOptions = config.options?.options as Array<{value: string, label: string}> || [];
        return (
          <select
            value={(currentValue as string) || ''}
            onChange={(e) => updateSearchValue(config.code, e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">{config.placeholder || `Select ${config.name}`}</option>
            {selectOptions.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        );

      case 'multi_select':
        const multiOptions = config.options?.options as Array<{value: string, label: string, count?: number}> || [];
        return (
          <MultiSelect
            options={multiOptions}
            value={(currentValue as string[]) || []}
            onValueChange={(value) => updateSearchValue(config.code, value)}
            placeholder={config.placeholder || `Select ${config.name}`}
            emptyText={`No ${config.name.toLowerCase()} found.`}
            showAllOption={true}
          />
        );

      case 'date_range':
        const dateValue = currentValue as { from?: string; to?: string } || {};
        return (
          <DateRangePicker
            startDate={dateValue.from || ''}
            endDate={dateValue.to || ''}
            onStartDateChange={(date) => updateSearchValue(config.code, { ...dateValue, from: date })}
            onEndDateChange={(date) => updateSearchValue(config.code, { ...dateValue, to: date })}
            placeholder={config.placeholder || `Select ${config.name.toLowerCase()} range`}
          />
        );

      case 'checkbox':
        const checkboxOptions = config.options?.options as Array<{value: string, label: string}> || [];
        return (
          <div className="space-y-2">
            {checkboxOptions.map(option => (
              <label key={option.value} className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={((currentValue as string[]) || []).includes(option.value)}
                  onChange={(e) => {
                    const current = (currentValue as string[]) || [];
                    const newValue = e.target.checked
                      ? [...current, option.value]
                      : current.filter(v => v !== option.value);
                    updateSearchValue(config.code, newValue);
                  }}
                  className="rounded"
                />
                <span className="text-sm">{option.label}</span>
              </label>
            ))}
          </div>
        );

      default:
        return (
          <Input
            placeholder={config.placeholder || `Enter ${config.name.toLowerCase()}`}
            value={(currentValue as string) || ''}
            onChange={(e) => updateSearchValue(config.code, e.target.value)}
            className="w-full"
          />
        );
    }
  };

  if (configsLoading) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <Loader2 className="h-6 w-6 animate-spin mr-2" />
            <span>Loading search configurations...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <div className="text-red-600 text-center">
            <p>Error: {error}</p>
            <Button variant="outline" onClick={loadSearchConfigs} className="mt-2">
              Retry
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  const basicConfigs = searchConfigs.filter(c => !c.isAdvanced);
  const advancedConfigs = searchConfigs.filter(c => c.isAdvanced);
  const activeSearchCount = searchValues.size;

  // 计算是否有任何可执行的搜索条件（增强搜索条件或常规筛选器变更）
  const hasAnySearchConditions = activeSearchCount > 0 || hasFilterChanges;

  return (
    <Card className={className}>
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Enhanced Search
            {activeSearchCount > 0 && (
              <Badge variant="secondary" className="ml-2">
                {activeSearchCount} active
              </Badge>
            )}
          </CardTitle>
          <div className="flex gap-2">
            {advancedConfigs.length > 0 && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowAdvanced(!showAdvanced)}
              >
                <Settings className="h-4 w-4 mr-1" />
                Advanced
              </Button>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* 基础搜索配置 */}
        {basicConfigs.length > 0 && (
          <div className="space-y-3">
            <h4 className="text-sm font-medium text-gray-700">Basic Filters</h4>
            {basicConfigs.map(config => (
              <div key={config.code} className="space-y-2">
                <Label htmlFor={config.code} className="text-sm font-medium">
                  {config.name}
                  {config.configType === 'MULTI_FIELD' && (
                    <Badge variant="outline" className="ml-2 text-xs">Multi-field</Badge>
                  )}
                  {config.configType === 'CROSS_TABLE' && (
                    <Badge variant="outline" className="ml-2 text-xs">Cross-table</Badge>
                  )}
                </Label>
                {config.description && (
                  <p className="text-xs text-gray-500">{config.description}</p>
                )}
                {renderSearchControl(config)}
              </div>
            ))}
          </div>
        )}

        {/* 高级搜索配置 */}
        {showAdvanced && advancedConfigs.length > 0 && (
          <div className="space-y-3 border-t pt-4">
            <h4 className="text-sm font-medium text-gray-700">Advanced Filters</h4>
            {advancedConfigs.map(config => (
              <div key={config.code} className="space-y-2">
                <Label htmlFor={config.code} className="text-sm font-medium">
                  {config.name}
                  <Badge variant="secondary" className="ml-2 text-xs">Advanced</Badge>
                  {config.configType === 'CROSS_TABLE' && (
                    <Badge variant="outline" className="ml-2 text-xs">Cross-table</Badge>
                  )}
                </Label>
                {config.description && (
                  <p className="text-xs text-gray-500">{config.description}</p>
                )}
                {renderSearchControl(config)}
              </div>
            ))}
          </div>
        )}

        {/* 操作按钮 */}
        <div className="flex gap-2 pt-4 border-t">
          <Button
            onClick={handleSearch}
            disabled={loading || !hasAnySearchConditions}
            className="flex-1"
          >
            {loading ? (
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            ) : (
              <Search className="mr-2 h-4 w-4" />
            )}
            Search
          </Button>
          <Button
            variant="outline"
            onClick={handleClear}
            disabled={loading || !hasAnySearchConditions}
          >
            <X className="mr-2 h-4 w-4" />
            Clear
          </Button>
        </div>

        {/* 搜索状态显示 */}
        {(activeSearchCount > 0 || hasFilterChanges) && (
          <div className="text-xs text-gray-500 pt-2 space-y-1">
            {activeSearchCount > 0 && (
              <div>
                {activeSearchCount} enhanced search condition{activeSearchCount > 1 ? 's' : ''} active
              </div>
            )}
            {hasFilterChanges && (
              <div className="text-orange-600">
                {Object.keys(pendingFilters).length} filter{Object.keys(pendingFilters).length > 1 ? 's' : ''} ready to apply
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
