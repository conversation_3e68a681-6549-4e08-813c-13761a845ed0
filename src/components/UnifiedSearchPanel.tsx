"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import EnhancedSearchPanel from './EnhancedSearchPanel';

interface UnifiedSearchPanelProps {
  database: string;
  onSearch: (searchData: any) => void;
  onClear: () => void;
  loading?: boolean;
  className?: string;
  mode?: 'enhanced'; // Only enhanced mode supported now
  showModeSwitch?: boolean;
}

export default function UnifiedSearchPanel({
  database,
  onSearch,
  onClear,
  loading = false,
  className = "",
  mode = 'enhanced',
  showModeSwitch = false
}: UnifiedSearchPanelProps) {
  const [searchConfigs, setSearchConfigs] = useState<any[]>([]);
  const [configsLoading, setConfigsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadConfigurations();
  }, [database, mode]);

  const loadConfigurations = async () => {
    try {
      setConfigsLoading(true);
      setError(null);

      // Only load SearchConfig (enhanced mode)
      const response = await fetch(`/api/search-config/${database}?mode=enhanced`);
      const result = await response.json();

      if (result.success) {
        setSearchConfigs(result.data || []);
      } else {
        throw new Error(result.error || 'Failed to load search configurations');
      }
    } catch (error) {
      console.error('加载搜索配置失败:', error);
      setError(error instanceof Error ? error.message : 'Unknown error');
    } finally {
      setConfigsLoading(false);
    }
  };

  const handleEnhancedSearch = (searchData: any) => {
    onSearch({
      type: 'enhanced',
      mode: 'searchconfig',
      data: searchData
    });
  };

  if (configsLoading) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
            <span className="ml-2 text-sm text-gray-600">Loading search configurations...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <div className="text-center">
            <div className="text-red-600 text-sm mb-2">⚠️ Failed to load search configurations</div>
            <div className="text-xs text-gray-500">{error}</div>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={loadConfigurations}
              className="mt-2"
            >
              Retry
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Enhanced Search only
  return (
    <div className={className}>
      {showModeSwitch && searchConfigs.length > 0 && (
        <div className="mb-4 flex items-center justify-between">
          <Badge variant="outline" className="text-xs">
            Enhanced Search ({searchConfigs.length} configs)
          </Badge>
        </div>
      )}
      <EnhancedSearchPanel
        database={database}
        onSearch={handleEnhancedSearch}
        onClear={onClear}
        loading={loading}
        className="border-0 shadow-none"
      />
    </div>
  );
}
