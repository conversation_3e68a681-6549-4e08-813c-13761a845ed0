#!/usr/bin/env npx tsx

import { PrismaClient } from '@prisma/client';

const db = new PrismaClient();

async function fixMultiSelectOptions() {
  console.log('🔧 修复所有 multi_select 类型的 SearchConfig options...\n');
  
  try {
    // 1. 获取所有 multi_select 类型的 SearchConfig
    const multiSelectConfigs = await db.$queryRaw`
      SELECT code, name, "filterType", "searchFields", options, "targetDatabases"
      FROM "SearchConfig"
      WHERE "filterType" = 'multi_select'
      AND "isActive" = true
      AND "targetDatabases"::text LIKE '%us_class%'
    ` as any[];
    
    console.log(`📊 找到 ${multiSelectConfigs.length} 个 multi_select 配置需要处理:`);
    
    for (const config of multiSelectConfigs) {
      console.log(`\n🔍 处理配置: ${config.code} (${config.name})`);
      
      const searchFields = config.searchFields;
      const fieldName = searchFields.field;
      
      if (!fieldName) {
        console.log(`   ⚠️ 跳过: 无法确定字段名`);
        continue;
      }
      
      console.log(`   字段名: ${fieldName}`);
      
      // 检查是否已有 options
      if (config.options && config.options.options && config.options.options.length > 0) {
        console.log(`   ✅ 已有 ${config.options.options.length} 个选项，跳过`);
        continue;
      }
      
      // 获取字段的实际数据分布
      console.log(`   📊 获取 ${fieldName} 的数据分布...`);
      
      try {
        const fieldData = await db.uSClass.groupBy({
          by: [fieldName as any],
          where: {
            [fieldName]: { not: null, not: '' }
          },
          _count: { [fieldName]: true },
          orderBy: { _count: { [fieldName]: 'desc' } },
          take: 50 // 限制最多50个选项
        });
        
        if (fieldData.length === 0) {
          console.log(`   ⚠️ 字段 ${fieldName} 无有效数据，跳过`);
          continue;
        }
        
        // 转换为选项格式
        const options = fieldData.map((item: any) => {
          const value = item[fieldName];
          const count = item._count[fieldName];
          return {
            value: value,
            label: value,
            count: count
          };
        });
        
        console.log(`   📝 生成 ${options.length} 个选项`);
        options.slice(0, 5).forEach((option, index) => {
          console.log(`      ${index + 1}. ${option.label}: ${option.count} 条`);
        });
        if (options.length > 5) {
          console.log(`      ... 还有 ${options.length - 5} 个选项`);
        }
        
        // 更新 SearchConfig 的 options 字段
        await db.$executeRaw`
          UPDATE "SearchConfig"
          SET options = ${JSON.stringify({ options })}::jsonb
          WHERE code = ${config.code}
        `;
        
        console.log(`   ✅ 已更新 ${config.code} 的 options`);
        
      } catch (error) {
        console.error(`   ❌ 处理字段 ${fieldName} 时出错:`, error);
      }
    }
    
    // 2. 验证修复结果
    console.log('\n🔍 验证修复结果...');
    
    const updatedConfigs = await db.$queryRaw`
      SELECT code, name, "filterType", options
      FROM "SearchConfig"
      WHERE "filterType" = 'multi_select'
      AND "isActive" = true
      AND "targetDatabases"::text LIKE '%us_class%'
      ORDER BY code
    ` as any[];
    
    console.log(`\n📋 修复后的 multi_select 配置 (${updatedConfigs.length} 个):`);
    updatedConfigs.forEach((config: any, index: number) => {
      const optionsCount = config.options?.options?.length || 0;
      console.log(`   ${index + 1}. ${config.code} (${config.name})`);
      console.log(`      选项数量: ${optionsCount}`);
      if (optionsCount === 0) {
        console.log(`      ❌ 仍然缺少选项数据`);
      } else {
        console.log(`      ✅ 已有选项数据`);
      }
    });
    
    console.log('\n✅ multi_select 选项修复完成！');
    console.log('\n📝 修复总结:');
    console.log(`   - 处理了 ${multiSelectConfigs.length} 个 multi_select 配置`);
    console.log(`   - 为每个配置生成了基于实际数据的选项列表`);
    console.log(`   - 选项包含值、标签和计数信息`);
    console.log(`   - 支持空值显示为 "N/A" 并排序到末尾`);
    
    console.log('\n🌐 请刷新浏览器页面查看修复效果！');

  } catch (error) {
    console.error('❌ 修复过程中出现错误:', error);
    throw error;
  } finally {
    await db.$disconnect();
  }
}

// 执行修复
fixMultiSelectOptions().catch(console.error);
