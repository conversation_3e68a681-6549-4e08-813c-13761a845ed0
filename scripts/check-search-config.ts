#!/usr/bin/env npx tsx

import { PrismaClient } from '@prisma/client';

const db = new PrismaClient();

async function checkSearchConfig() {
  console.log('🔍 检查 SearchConfig 表中的配置...\n');
  
  try {
    // 1. 检查 SearchConfig 表是否存在
    const tableExists = await db.$queryRaw`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'SearchConfig'
      );
    ` as any[];
    
    console.log(`📋 SearchConfig 表存在: ${tableExists[0]?.exists || false}`);
    
    if (!tableExists[0]?.exists) {
      console.log('❌ SearchConfig 表不存在，搜索配置可能还在 FieldConfig 表中');
      
      // 检查 FieldConfig 中的筛选配置
      console.log('\n📊 检查 FieldConfig 中的筛选配置...');
      
      const filterableFields = await db.fieldConfig.findMany({
        where: {
          databaseCode: 'us_class',
          isFilterable: true,
          isActive: true
        },
        select: {
          fieldName: true,
          displayName: true,
          filterType: true,
          isFilterable: true,
          isVisible: true,
          sortOrder: true
        },
        orderBy: {
          sortOrder: 'asc'
        }
      });
      
      console.log(`\n   找到 ${filterableFields.length} 个可筛选字段:`);
      filterableFields.forEach((field, index) => {
        console.log(`   ${index + 1}. ${field.fieldName} (${field.displayName})`);
        console.log(`      筛选类型: ${field.filterType}`);
        console.log(`      可见: ${field.isVisible}`);
        console.log('');
      });
      
      // 特别检查 review_panel
      const reviewPanelField = filterableFields.find(f => f.fieldName === 'review_panel');
      if (reviewPanelField) {
        console.log('✅ review_panel 字段配置:');
        console.log(`   筛选类型: ${reviewPanelField.filterType}`);
        console.log(`   可筛选: ${reviewPanelField.isFilterable}`);
        console.log(`   可见: ${reviewPanelField.isVisible}`);
      } else {
        console.log('❌ review_panel 字段未配置为可筛选');
      }
      
      return;
    }
    
    // 2. 如果 SearchConfig 表存在，检查其中的配置
    const searchConfigs = await db.$queryRaw`
      SELECT code, name, "configType", "filterType", "searchFields", "isActive", "isAdvanced"
      FROM "SearchConfig"
      WHERE "targetDatabases"::text LIKE '%us_class%'
      AND "isActive" = true
    ` as any[];
    
    console.log(`\n📊 us_class 相关的 SearchConfig 配置 (${searchConfigs.length} 个):`);
    searchConfigs.forEach((config: any, index: number) => {
      console.log(`   ${index + 1}. ${config.code} (${config.name})`);
      console.log(`      配置类型: ${config.configType}`);
      console.log(`      筛选类型: ${config.filterType}`);
      console.log(`      高级搜索: ${config.isAdvanced}`);
      console.log(`      搜索字段: ${JSON.stringify(config.searchFields, null, 2)}`);
      console.log('');
    });
    
    // 3. 检查是否有 review_panel 相关的配置
    const reviewPanelConfigs = await db.$queryRaw`
      SELECT code, name, "filterType", "searchFields"
      FROM "SearchConfig"
      WHERE (code LIKE '%review_panel%' OR name LIKE '%review_panel%' OR name LIKE '%审查小组%')
      AND "isActive" = true
    ` as any[];
    
    console.log(`🔍 review_panel 相关的 SearchConfig (${reviewPanelConfigs.length} 个):`);
    if (reviewPanelConfigs.length === 0) {
      console.log('   ❌ 未找到 review_panel 相关的搜索配置');
    } else {
      reviewPanelConfigs.forEach((config: any, index: number) => {
        console.log(`   ${index + 1}. ${config.code} (${config.name})`);
        console.log(`      筛选类型: ${config.filterType}`);
        console.log(`      搜索字段: ${JSON.stringify(config.searchFields, null, 2)}`);
      });
    }
    
  } catch (error) {
    console.error('❌ 检查过程中出现错误:', error);
  }
  
  // 4. 无论如何都检查 FieldConfig 中的 review_panel 配置
  console.log('\n📋 检查 FieldConfig 中的 review_panel 配置...');
  try {
    const fieldConfig = await db.fieldConfig.findUnique({
      where: {
        databaseCode_fieldName: {
          databaseCode: 'us_class',
          fieldName: 'review_panel'
        }
      },
      select: {
        fieldName: true,
        displayName: true,
        isFilterable: true,
        filterType: true,
        isVisible: true,
        isActive: true,
        sortOrder: true,
        listOrder: true
      }
    });
    
    if (fieldConfig) {
      console.log('   ✅ 找到 review_panel 字段配置:');
      console.log(`      字段名: ${fieldConfig.fieldName}`);
      console.log(`      显示名: ${fieldConfig.displayName}`);
      console.log(`      可筛选: ${fieldConfig.isFilterable}`);
      console.log(`      筛选类型: ${fieldConfig.filterType}`);
      console.log(`      可见: ${fieldConfig.isVisible}`);
      console.log(`      激活: ${fieldConfig.isActive}`);
      console.log(`      排序: ${fieldConfig.sortOrder}`);
      console.log(`      列表顺序: ${fieldConfig.listOrder}`);
    } else {
      console.log('   ❌ 未找到 review_panel 字段配置');
    }
  } catch (error) {
    console.error('   ❌ 检查 FieldConfig 时出错:', error);
  }
  
  await db.$disconnect();
}

checkSearchConfig().catch(console.error);
