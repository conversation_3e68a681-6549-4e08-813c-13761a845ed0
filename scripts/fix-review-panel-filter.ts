#!/usr/bin/env npx tsx

import { PrismaClient } from '@prisma/client';

const db = new PrismaClient();

async function fixReviewPanelFilter() {
  console.log('🔧 修复 us_class 数据库的 review_panel 筛选器功能...\n');
  
  try {
    // 1. 修复 review_panel 字段配置
    console.log('📋 1. 修复 review_panel 字段配置...');
    
    const reviewPanelConfig = await db.fieldConfig.update({
      where: {
        databaseCode_fieldName: {
          databaseCode: 'us_class',
          fieldName: 'review_panel',
        },
      },
      data: {
        displayName: '审查小组',
        isFilterable: true,        // 启用筛选功能
        filterType: 'multi_select', // 设置为多选筛选
        isVisible: true,           // 设置为可见（可选，如果希望在列表中显示）
        listOrder: 6,              // 设置列表显示顺序
        searchType: 'exact',       // 精确匹配
        isActive: true,
      },
    });
    
    console.log(`  ✅ review_panel 字段配置已更新:`);
    console.log(`     - 可筛选: ${reviewPanelConfig.isFilterable}`);
    console.log(`     - 筛选类型: ${reviewPanelConfig.filterType}`);
    console.log(`     - 可见: ${reviewPanelConfig.isVisible}`);
    console.log(`     - 显示名称: ${reviewPanelConfig.displayName}`);

    // 2. 同时修复其他重要筛选字段
    console.log('\n📋 2. 修复其他重要筛选字段...');
    
    const fieldsToFix = [
      {
        fieldName: 'deviceclass',
        displayName: '器械类别',
        filterType: 'multi_select',
        isFilterable: true,
        isVisible: true,
        listOrder: 3,
      },
      {
        fieldName: 'medicalspecialty',
        displayName: '医学专科',
        filterType: 'multi_select',
        isFilterable: true,
        isVisible: true,
        listOrder: 4,
      },
      {
        fieldName: 'gmpexemptflag',
        displayName: 'GMP豁免',
        filterType: 'checkbox',
        isFilterable: true,
        isVisible: false, // 保持不在列表显示，但可筛选
        listOrder: 0,
      },
      {
        fieldName: 'thirdpartyflag',
        displayName: '第三方标志',
        filterType: 'checkbox',
        isFilterable: true,
        isVisible: false,
        listOrder: 0,
      },
    ];

    for (const fieldConfig of fieldsToFix) {
      const updatedConfig = await db.fieldConfig.update({
        where: {
          databaseCode_fieldName: {
            databaseCode: 'us_class',
            fieldName: fieldConfig.fieldName,
          },
        },
        data: {
          displayName: fieldConfig.displayName,
          isFilterable: fieldConfig.isFilterable,
          filterType: fieldConfig.filterType as any,
          isVisible: fieldConfig.isVisible,
          listOrder: fieldConfig.listOrder,
          searchType: 'exact',
          isActive: true,
        },
      });
      
      console.log(`  ✅ ${fieldConfig.fieldName} (${fieldConfig.displayName}): ${fieldConfig.filterType}`);
    }

    // 3. 验证修复结果
    console.log('\n🔍 3. 验证修复结果...');
    
    const filterableFields = await db.fieldConfig.findMany({
      where: {
        databaseCode: 'us_class',
        isFilterable: true,
        isActive: true,
      },
      select: {
        fieldName: true,
        displayName: true,
        filterType: true,
        isFilterable: true,
        isVisible: true,
        listOrder: true,
      },
      orderBy: {
        sortOrder: 'asc',
      },
    });

    console.log('\n📊 当前可筛选的字段:');
    filterableFields.forEach((field, index) => {
      console.log(`   ${index + 1}. ${field.fieldName} (${field.displayName})`);
      console.log(`      筛选类型: ${field.filterType}`);
      console.log(`      可见: ${field.isVisible}`);
      console.log(`      列表顺序: ${field.listOrder}`);
      console.log('');
    });

    // 4. 检查 review_panel 数据分布
    console.log('📊 4. 检查 review_panel 数据分布...');
    
    const reviewPanelStats = await db.uSClass.groupBy({
      by: ['review_panel'],
      where: {
        review_panel: { not: null, not: '' }
      },
      _count: { review_panel: true },
      orderBy: { _count: { review_panel: 'desc' } },
      take: 10
    });

    console.log('\n   review_panel 数据分布（前10）:');
    let totalCount = 0;
    reviewPanelStats.forEach((stat, index) => {
      const count = stat._count.review_panel;
      totalCount += count;
      console.log(`   ${index + 1}. ${stat.review_panel || 'N/A'}: ${count} 条`);
    });
    console.log(`   总计: ${totalCount} 条有效数据`);

    // 5. 检查空值情况
    const nullCount = await db.uSClass.count({
      where: {
        OR: [
          { review_panel: null },
          { review_panel: '' }
        ]
      }
    });

    console.log(`   空值/null: ${nullCount} 条`);

    console.log('\n✅ review_panel 筛选器功能修复完成！');
    console.log('\n📝 修复内容总结:');
    console.log('   1. ✅ review_panel 字段设置为可筛选 (isFilterable: true)');
    console.log('   2. ✅ review_panel 筛选类型设置为多选 (filterType: multi_select)');
    console.log('   3. ✅ review_panel 字段设置为可见 (isVisible: true)');
    console.log('   4. ✅ 其他重要字段的筛选功能也已修复');
    console.log('   5. ✅ 数据验证显示有足够的数据支持筛选功能');
    
    console.log('\n🌐 请刷新浏览器页面查看修复效果！');

  } catch (error) {
    console.error('❌ 修复过程中出现错误:', error);
    throw error;
  } finally {
    await db.$disconnect();
  }
}

// 执行修复
fixReviewPanelFilter().catch(console.error);
